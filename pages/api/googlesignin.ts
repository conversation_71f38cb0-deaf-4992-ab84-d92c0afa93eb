import axios from 'axios';
import { config } from 'config';

var cookie = require('cookie');
var escapeHtml = require('escape-html');
var http = require('http');
var url = require('url');

export default async function handler(req: any, res: any) {
  try {
    const accessToken = req.body;
    console.log('🔑 Google signin API called with token:', accessToken ? 'Present' : 'Missing');

    const token = await fetch(`${config?.restPrefix}/user-auth/google/sign-in`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        accessToken,
      }),
    });

    const data = await token.json();
    console.log('🔄 Backend response:', data);
    console.log('🔄 Backend status:', token.status);

    if (data.data && data.data.token) {
      res.setHeader(
        'Set-Cookie',
        cookie.serialize('token', data.data.token, {
          httpOnly: true,
          maxAge: 60 * 60 * 24 * 7,
          sameSite: 'strict',
          path: '/',
        })
      );
      console.log('✅ Cookie set successfully');
    } else {
      console.log('❌ No token in response');
    }

    res.json(data);
    axios.defaults.headers.common['Authorization'] = `${token}`;
  } catch (error) {
    console.error('💥 Google signin API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
