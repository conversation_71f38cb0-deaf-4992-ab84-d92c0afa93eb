import Link from 'next/link';

import { userAPI } from 'APIs';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { storeOtp, storeUserDetails } from 'store/slices/signUpSlice';

import Loading from '@/modules/common/loader';
import { handleLogout } from 'helper/handleLogout';
import { OtpForm } from './components/OtpForm';
import { UserDetailsForm } from './components/UserDetailsForm';
import Image from 'next/image';

interface User {
  username: string;
  name: string;
  password: string;
  otp: number;
}

const SignUp: React.FC = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  let username = '';
  let loggedInUsingEmail = false;

  const [loading, setLoading] = useState(false);
  const [submitButtonState, setSubmitButtonState] =
    useState<string>('userdetails');

  const userDetails = useAppSelector(
    (state) => state.persistedReducer.signUp.user
  );

  const prepareData = (formData: User) => {
    let data;
    let regex = new RegExp('[a-z0-9]+@[a-z]+.[a-z]{2,3}');
    const isEmail = regex.test(formData.username);
    username = formData.username;
    isEmail ? (loggedInUsingEmail = true) : (loggedInUsingEmail = false);
    data = {
      email: formData.username,
      name: formData.name,
      password: formData.password,
    };
    return data;
  };

  const handleUserDetailsFormSubmit = async (userDetails: User) => {
    try {
      const data = prepareData(userDetails);
      const res = await userAPI.sendOTP(data);
      if ('data' in res!) {
        const responseMessage = res?.data?.message!.split(' ');
        const otpValue = responseMessage![responseMessage?.length! - 1];
        dispatch(storeUserDetails(data));
        dispatch(storeOtp(parseInt(otpValue as string)));
        setSubmitButtonState('otp');
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {
      toast.error('Failed to sign-up.', {
        containerId: 'bottom-right',
      });
    }
  };

  const handleOtpFormSubmit = async (otpValue: number) => {
    try {
      let data;
      data = {
        email: userDetails.email!,
        otp: otpValue,
      };
      setLoading(true);
      const res = await userAPI.signUp(data);
      setLoading(false);
      if ('data' in res) {
        router.push('/account/sign-in');
        toast.success('Account created successfully!', {
          containerId: 'bottom-right',
        });
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {
      setLoading(false);
      toast.error('User creation failed. Try again.', {
        containerId: 'bottom-right',
      });
    }
  };

  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  // Removed automatic logout logic that was causing issues
  // useEffect(() => {
  //   if (token) {
  //     handleLogout(localStorage, dispatch, providerName, router);
  //   }
  // }, []);

  if (loading) {
    return <Loading />;
  }

  return (
    <>
      {/* <Breadcrumb
        title={t('register:page_title')}
        pathArray={[`${t('common:home')}`, `${t('register:page_title')}`]}
        linkArray={['/market', '/account/sign-up']}
      /> */}
      <div className=" flex items-center justify-center">
        <Image src="/auth.png" alt="auth" width={450} height={200} />
        <div className="flex flex-wrap justify-center">
          <div
            className="my-8 mx-3 flex flex-col py-7"
            style={{ width: ' 35rem ', height: 'auto' }}
          >
            <h2 className="mx-3 text-center text-3xl font-bold text-gray-800">
              {/* {t('register:page_title')} */}
              Welcome to fitsomnia
            </h2>
            <p className="mx-5 mt-2 mb-6 text-center text-black">
              {/* {t('register:registration_form_header')} */}
              The ultimate all in one social media app
            </p>
            <h2 className="mx-3 mt-8 text-center text-3xl font-bold text-black">
              Registration
            </h2>

            <div className="m-5 my-3 sm:m-5 md:mx-10 lg:mx-10 xl:mx-10">
              {submitButtonState === 'userdetails' && (
                <>
                  <UserDetailsForm
                    handleUserDetailsFormSubmit={handleUserDetailsFormSubmit}
                  />

                  <div className="flex items-center justify-center gap-2">
                    <p>Already have an account?</p>
                    <div>
                      <Link
                        prefetch={false}
                        data-testid="login-account-link"
                        href="/account/sign-in"
                        className="text-primary underline"
                      >
                        Log In
                      </Link>
                    </div>
                  </div>
                </>
              )}

              {submitButtonState === 'otp' && (
                <OtpForm handleOtpFormSubmit={handleOtpFormSubmit} />
              )}
              {/* <div className="text-decoration-none font-weight-light my-2 text-gray-800">
              <Link prefetch={false} href="/market">
                {t('register:return_to_store')}
              </Link>
            </div> */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SignUp;
