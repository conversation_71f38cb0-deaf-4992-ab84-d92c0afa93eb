import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';

import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';

import Loading from '@/modules/common/loader';
import { handleLogout } from 'helper/handleLogout';
import Image from 'next/image';
import { SignInForm } from './SignInForm';
import GoogleLogo from '@/modules/common/icons/googleLogo';
import { signIn } from 'next-auth/react';
import { storeProvider } from 'store/slices/providerSlice';
import FacebookLogo from '@/modules/common/icons/facebookLogo';

const Signin: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();

  const [loader, setLoader] = useState(false);

  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  useEffect(() => {
    if (token) {
      handleLogout(localStorage, dispatch, providerName, router);
    }
  }, []);

  if (loader) return <Loading />;
  return (
    <>
      {/* <Breadcrumb
        title={`${t('common:account')}`}
        pathArray={[`${t('common:home')}`, `${t('common:account')}`]}
        linkArray={['/market', '/account/sign-in']}
      /> */}
      <div className=" flex items-center justify-center">
        <Image src="/auth.png" alt="auth" width={450} height={200} />
        <div className="flex flex-wrap justify-center">
          <div
            className="my-20 mx-3 flex flex-col py-7"
            style={{
              width: ' 35rem ',
              height: 'auto',
              // background: '#f3f3f3',
            }}
          >
            <h2 className="mx-3 text-center text-3xl font-bold text-black">
              {/* {t('common:login')} */}
              Welcome to fitsomnia
            </h2>
            <p className="mx-5 mt-2 mb-6 text-center text-black">
              {/* {t('login:login_form_header')} */}
              The ultimate all in one social media app
            </p>
            <h2 className="mx-3 mt-10 text-center text-3xl font-bold text-black">
              Login
            </h2>
            <div className="m-5 my-3 sm:m-5 md:mx-10 lg:mx-10 xl:mx-10">
              <SignInForm setLoader={setLoader} />

              <div className="flex items-center justify-center gap-2">
                <p>Don’t have an account?</p>
                <div>
                  <Link
                    prefetch={false}
                    data-testid="create-account-link"
                    href="/account/sign-up"
                    className="text-primary underline"
                  >
                    Create Account
                  </Link>
                </div>
              </div>

              {/* <NewForm /> */}
              {/* <div className="flex flex-row"> */}

              {/* <div className="text-decoration-none font-weight-light mt-3 text-gray-600 hover:text-primary">
                <Link
                  prefetch={false}
                  data-testid="create-account-link"
                  href="/account/sign-up"
                >
                  {t('login:create_account')}
                </Link>
              </div> */}
              <div className="mt-3">
                <p className="ml-1 text-gray-600">{t('login:social_login')}</p>
              </div>
              <div className="flex flex-wrap">
                <button
                  className="mx-1 mt-3 flex flex-wrap"
                  onClick={async () => {
                    await signIn('google', { callbackUrl: '/post/newsfeed' });
                    dispatch(storeProvider('google'));
                  }}
                >
                  <GoogleLogo />
                  <p className="ml-1 mt-1 text-xs">Google</p>
                </button>
                <div className="mt-3">
                  <p className="ml-1 text-gray-600">{t('common:or')}</p>
                </div>
                <button
                  className="mx-1 mt-3 flex flex-wrap"
                  onClick={async () => {
                    await signIn('facebook', { callbackUrl: '/post/newsfeed' });
                    dispatch(storeProvider('facebook'));
                  }}
                >
                  <FacebookLogo />
                  <p className="mt-1 text-xs">Facebook</p>
                </button>
              </div>
              {/* </div> */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Signin;
