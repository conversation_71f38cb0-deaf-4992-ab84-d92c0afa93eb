import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useAppSelector } from 'store/hooks/index';
import Loading from '@/modules/common/loader';
import { useSession } from 'next-auth/react';

const WithAuth = (Component: React.FC) => {
  const Auth = (props: any) => {
    const router = useRouter();
    const { data: session, status } = useSession();
    const [isChecking, setIsChecking] = useState(true);
    const isLoggedIn = useAppSelector(
      (state) => state.persistedReducer.auth.access_token
    );

    useEffect(() => {
      // If NextAuth is still loading, wait
      if (status === 'loading') {
        return;
      }

      // If we have a session but no Redux token, give time for session handling
      if (session && !isLoggedIn) {
        const timer = setTimeout(() => {
          setIsChecking(false);
        }, 3000); // Wait 3 seconds for session handling to complete
        return () => clearTimeout(timer);
      }

      // If no session and no token, redirect to sign in
      if (!session && !isLoggedIn) {
        router.push('/account/sign-in');
        return;
      }

      // If we have a token, we're good
      if (isLoggedIn) {
        setIsChecking(false);
      }
    }, [session, isLoggedIn, status, router]);

    // Show loading while checking authentication
    if (status === 'loading' || isChecking) {
      return <Loading />;
    }

    // If not logged in, show loading (will redirect)
    if (!isLoggedIn) {
      return <Loading />;
    }

    // Render the component if authenticated
    return <Component {...props} />;
  };

  return Auth;
};

export default WithAuth;
