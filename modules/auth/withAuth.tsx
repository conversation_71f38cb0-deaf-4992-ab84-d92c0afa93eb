import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useAppSelector } from 'store/hooks/index';
import Loading from '@/modules/common/loader';
import { useSession } from 'next-auth/react';

const WithAuth = (Component: React.FC) => {
  const Auth = (props: any) => {
    const router = useRouter();
    const { data: session, status } = useSession();
    const [isChecking, setIsChecking] = useState(true);
    const isLoggedIn = useAppSelector(
      (state) => state.persistedReducer.auth.access_token
    );

    useEffect(() => {
      console.log('🔐 WithAuth check:', { status, session: !!session, isLoggedIn: !!isLoggedIn, isChecking });

      // If NextAuth is still loading, wait
      if (status === 'loading') {
        return;
      }

      // If we have a token, we're authenticated - stop checking
      if (isLoggedIn) {
        console.log('✅ User is authenticated, allowing access');
        setIsChecking(false);
        return;
      }

      // If we have a session but no Redux token, give time for session handling
      if (session && !isLoggedIn) {
        console.log('⏳ Session found but no Redux token, waiting...');
        const timer = setTimeout(() => {
          console.log('⏰ Timeout reached, stopping check');
          setIsChecking(false);
        }, 5000); // Wait 5 seconds for session handling to complete
        return () => clearTimeout(timer);
      }

      // If no session and no token, redirect to sign in
      if (!session && !isLoggedIn) {
        console.log('🚫 No session and no token, redirecting to sign-in');
        router.push('/account/sign-in');
        return;
      }

      // Default case - stop checking
      setIsChecking(false);
    }, [session, isLoggedIn, status, router, isChecking]);

    // Show loading while checking authentication
    if (status === 'loading' || isChecking) {
      console.log('🔄 Showing loading:', { status, isChecking });
      return <Loading />;
    }

    // If not logged in, show loading (will redirect)
    if (!isLoggedIn) {
      console.log('🚫 Not logged in, showing loading');
      return <Loading />;
    }

    // Render the component if authenticated
    console.log('🎉 Rendering protected component');
    return <Component {...props} />;
  };

  return Auth;
};

export default WithAuth;
