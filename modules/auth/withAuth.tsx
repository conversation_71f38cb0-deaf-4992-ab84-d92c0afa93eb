import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from 'store/hooks/index';
import Loading from '@/modules/common/loader';
import { useSession, getSession } from 'next-auth/react';
import axios from 'axios';
import { storeUserToken } from 'store/slices/authSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { storeAllCartItems } from 'store/slices/cartSlice';
import { storeWishlist } from 'store/slices/productsSlice';
import { storeCompare } from 'store/slices/compareSlice';
import { userAPI } from 'APIs';
import { toast } from 'react-toastify';

const WithAuth = (Component: React.FC) => {
  const Auth = (props: any) => {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const { data: session, status } = useSession();
    const [isChecking, setIsChecking] = useState(true);
    const [sessionHandled, setSessionHandled] = useState(false);
    const isLoggedIn = useAppSelector(
      (state) => state.persistedReducer.auth.access_token
    );
    const providerName = useAppSelector(
      (state) => state?.persistedReducer?.provider?.provider
    );

    const handleSession = async () => {
      if (sessionHandled) return; // Prevent multiple calls

      console.log('🔍 WithAuth: Handling session...');
      setSessionHandled(true);

      try {
        console.log('🔑 Getting access token...');
        const accessToken = (
          await axios.get('http://localhost:4003/api/auth/tokenHandler')
        ).data.token;
        console.log('🎫 Access token:', accessToken ? 'Found' : 'Not found');

        // Determine provider from session if not in Redux
        let sessionProvider = 'google';
        if (session?.account?.provider) {
          sessionProvider = session.account.provider;
        } else if (session?.user?.email) {
          sessionProvider = 'google';
        } else {
          sessionProvider = 'facebook';
        }

        const actualProvider = providerName || sessionProvider;
        console.log('🏪 Using provider:', actualProvider);

        const url = actualProvider === 'google' ? '/api/googlesignin' : '/api/facebooksignin';

        const tokenfromBE = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(accessToken),
        });

        const res = await tokenfromBE.json();
        console.log('🔄 Backend response:', res);

        if ('data' in res && res.data?.token) {
          console.log('✅ Storing token in Redux...');
          dispatch(storeUserToken(res.data.token));

          // Fetch user data
          const userResponse = await userAPI.getCustomer(res.data.token);
          if ('data' in userResponse) {
            dispatch(storeCustomerDetails(userResponse.data));
            dispatch(storeAddresses(userResponse.data.addresses || []));
          }

          setIsChecking(false);
          toast.success('Logged in successfully!', { containerId: 'bottom-right' });
        } else {
          console.log('❌ Backend authentication failed');
          setIsChecking(false);
          router.push('/account/sign-in');
        }
      } catch (error) {
        console.error('💥 Session handling error:', error);
        setIsChecking(false);
        router.push('/account/sign-in');
      }
    };

    useEffect(() => {
      console.log('🔐 WithAuth check:', { status, session: !!session, isLoggedIn: !!isLoggedIn, isChecking, sessionHandled });

      // If NextAuth is still loading, wait
      if (status === 'loading') {
        return;
      }

      // If we have a token, we're authenticated - stop checking
      if (isLoggedIn) {
        console.log('✅ User is authenticated, allowing access');
        setIsChecking(false);
        return;
      }

      // If we have a session but no Redux token, handle the session
      if (session && !isLoggedIn && !sessionHandled) {
        console.log('🔄 Session found but no Redux token, handling session...');
        handleSession();
        return;
      }

      // If no session and no token, redirect to sign in
      if (!session && !isLoggedIn) {
        console.log('🚫 No session and no token, redirecting to sign-in');
        router.push('/account/sign-in');
        return;
      }

      // Default case - stop checking
      setIsChecking(false);
    }, [session, isLoggedIn, status, router, sessionHandled]);

    // Show loading while checking authentication
    if (status === 'loading' || isChecking) {
      console.log('🔄 Showing loading:', { status, isChecking });
      return <Loading />;
    }

    // If not logged in, show loading (will redirect)
    if (!isLoggedIn) {
      console.log('🚫 Not logged in, showing loading');
      return <Loading />;
    }

    // Render the component if authenticated
    console.log('🎉 Rendering protected component');
    return <Component {...props} />;
  };

  return Auth;
};

export default WithAuth;
